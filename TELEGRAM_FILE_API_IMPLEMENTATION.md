# Telegram File API Implementation

## Overview

This implementation provides two API methods for downloading files from Telegram:

1. **Bot API** (`/api/telegram/get-file`) - For files accessible via Telegram Bot API
2. **MTProto API** (`/api/telegram/get-mtproto-file`) - For files requiring MTProto client API

## Files Created

### API Routes
- `src/app/api/telegram/get-file/route.ts` - Bot API file download
- `src/app/api/telegram/get-mtproto-file/route.ts` - MTProto API file download

### Components
- `src/components/telegram-file-viewer.tsx` - React component for testing file downloads

### Pages
- Updated `src/app/marketplace/page.tsx` - Added demo component

## Environment Variables Required

```env
TELEGRAM_API_ID=14431416
TELEGRAM_API_HASH=da8fa0a17dd9e0c1b9e420d73a39a710
TELEGRAM_BOT_TOKEN=**********************************************
```

## API Endpoints

### 1. Bot API - `/api/telegram/get-file`

**Usage:**
```
GET /api/telegram/get-file?fileId=<TELEGRAM_BOT_FILE_ID>
```

**Parameters:**
- `fileId` - Valid Telegram Bot API file ID (obtained from bot interactions)

**Response:**
- Success: Returns the file as binary data with appropriate content-type headers
- Error: JSON error response with details

**Example:**
```javascript
const response = await fetch('/api/telegram/get-file?fileId=BAADBAADrwADBREAAWdVAAE...');
const blob = await response.blob();
```

### 2. MTProto API - `/api/telegram/get-mtproto-file`

**Usage:**
```
GET /api/telegram/get-mtproto-file?documentId=<DOCUMENT_ID>
```

**Parameters:**
- `documentId` - Telegram document ID (requires access_hash for full functionality)

**Current Status:**
- ⚠️ **Incomplete**: Requires both document ID and access_hash
- Returns error explaining the limitation
- Framework is in place for future enhancement

## File ID Types and Compatibility

### Bot API File IDs
- Format: Base64-encoded strings (e.g., `BAADBAADrwADBREAAWdVAAE...`)
- Obtained from: Bot interactions, file uploads, media messages
- Valid for: Limited time (usually 1 hour)
- Works with: `/api/telegram/get-file`

### MTProto Document IDs
- Format: Numeric strings (e.g., `6005564615793050414`)
- Obtained from: MTProto API calls, client applications
- Requires: Additional `access_hash` for download
- Works with: `/api/telegram/get-mtproto-file` (when enhanced)

## Testing the Implementation

### Using the Demo Component

1. Navigate to `http://localhost:3001/marketplace`
2. Find the "Telegram File Demo" section
3. Select API type (Bot API or MTProto API)
4. Click "Fetch File" to test

### Expected Results

**With Bot API:**
- File ID `6005564615793050414` will fail (invalid Bot API file ID)
- Error: "Bad Request: invalid file_id"

**With MTProto API:**
- File ID `6005564615793050414` will fail (missing access_hash)
- Error: "Cannot download file without access_hash"

## Getting Valid File IDs

### For Bot API Testing
1. Send a file to your bot via Telegram
2. Extract the `file_id` from the message
3. Use that `file_id` with the Bot API endpoint

### For MTProto API Testing
1. Need complete document reference with access_hash
2. Typically obtained from MTProto API responses
3. Format: `{id: "6005564615793050414", access_hash: "1234567890"}`

## Implementation Notes

### Bot API Limitations
- File size limit: 20MB
- File IDs expire after ~1 hour
- Only works with files sent to/through the bot

### MTProto API Advantages
- No file size limits
- Persistent file references
- Access to all Telegram content
- Requires user/bot authentication

### Security Considerations
- Bot token is server-side only
- File downloads are proxied through your API
- Implement rate limiting for production use
- Consider caching for frequently accessed files

## Future Enhancements

1. **Complete MTProto Implementation**
   - Add access_hash parameter support
   - Implement proper document reference handling
   - Add authentication session management

2. **File Caching**
   - Cache downloaded files locally
   - Implement cache invalidation
   - Add CDN integration

3. **Error Handling**
   - Better error messages
   - Retry mechanisms
   - Fallback strategies

4. **Performance Optimization**
   - Streaming for large files
   - Compression support
   - Parallel chunk downloads

## Usage Examples

### React Component Usage
```tsx
import TelegramFileViewer from '@/components/telegram-file-viewer';

function MyComponent() {
  return (
    <TelegramFileViewer fileId="your-file-id-here" />
  );
}
```

### Direct API Usage
```javascript
// Bot API
const botApiResponse = await fetch('/api/telegram/get-file?fileId=BAADBAADrwADBREAAWdVAAE...');

// MTProto API (when enhanced)
const mtprotoResponse = await fetch('/api/telegram/get-mtproto-file?documentId=6005564615793050414&accessHash=1234567890');
```

## Troubleshooting

### Common Issues
1. **"Invalid file_id"** - Using MTProto document ID with Bot API
2. **"Cannot download file without access_hash"** - MTProto API needs enhancement
3. **"File not found"** - Bot API file ID expired or invalid
4. **"Unauthorized"** - Check environment variables

### Debug Steps
1. Check browser console for detailed error messages
2. Verify environment variables are loaded
3. Test with known valid file IDs
4. Check server logs for API responses
