#!/usr/bin/env node

const fs = require("fs");
const path = require("path");
const { execSync } = require("child_process");

// Load environment variables from .env file
function loadEnvFile() {
  const envPath = path.join(__dirname, ".env.example");

  if (!fs.existsSync(envPath)) {
    console.error(
      "❌ .env file not found. Please create one based on .env.example"
    );
    process.exit(1);
  }

  const envContent = fs.readFileSync(envPath, "utf8");
  const envVars = {};

  envContent.split("\n").forEach((line) => {
    line = line.trim();
    if (line && !line.startsWith("#")) {
      const [key, ...valueParts] = line.split("=");
      if (key && valueParts.length > 0) {
        envVars[key.trim()] = valueParts
          .join("=")
          .trim()
          .replace(/^["']|["']$/g, "");
      }
    }
  });

  return envVars;
}

// Execute firebase command
function executeFirebaseCommand(command) {
  try {
    console.log(`🔧 Executing: ${command}`);
    execSync(command, { stdio: "inherit", cwd: path.dirname(__dirname) });
    return true;
  } catch (error) {
    console.error(`❌ Failed to execute: ${command}`);
    console.error(error.message);
    return false;
  }
}

// Main function to set up Firebase config
function setupFirebaseConfig() {
  console.log("🚀 Setting up Firebase Functions configuration...\n");

  const envVars = loadEnvFile();

  // Define the mapping between env variables and Firebase config paths
  const configMappings = [
    // App configuration
    {
      env: "NODE_ENV",
      config: "app.environment",
      required: false,
      default: "development",
    },
    { env: "FIREBASE_PROJECT_ID", config: "app.project_id", required: true },

    // Telegram configuration
    { env: "TELEGRAM_BOT_TOKEN", config: "telegram.bot_token", required: true },

    // TON configuration
    {
      env: "TON_RPC_URL_TESTNET",
      config: "ton.rpc_url_testnet",
      required: false,
    },
    {
      env: "TON_RPC_URL_MAINNET",
      config: "ton.rpc_url_mainnet",
      required: false,
    },
    {
      env: "TON_MARKETPLACE_WALLET",
      config: "ton.marketplace_wallet",
      required: true,
    },
    {
      env: "TON_NETWORK",
      config: "ton.network",
      required: false,
      default: "mainnet",
    },
    { env: "TONCENTER_API_KEY", config: "ton.api_key", required: false },

    // Firebase service account (if needed)
    {
      env: "FIREBASE_SERVICE_ACCOUNT_KEY",
      config: "firebase.service_account_key",
      required: false,
    },
  ];

  let hasErrors = false;
  const commands = [];

  // Validate required variables and prepare commands
  configMappings.forEach((mapping) => {
    let value = envVars[mapping.env];

    if (!value && mapping.required) {
      console.error(
        `❌ Required environment variable ${mapping.env} is missing`
      );
      hasErrors = true;
      return;
    }

    if (!value && mapping.default) {
      value = mapping.default;
      console.log(`ℹ️  Using default value for ${mapping.env}: ${value}`);
    }

    if (value) {
      // Escape quotes and special characters for shell
      const escapedValue = value.replace(/"/g, '\\"');
      commands.push(
        `firebase functions:config:set ${mapping.config}="${escapedValue}"`
      );
    }
  });

  if (hasErrors) {
    console.error(
      "\n❌ Configuration setup failed due to missing required variables."
    );
    console.error(
      "Please check your .env file and ensure all required variables are set."
    );
    process.exit(1);
  }

  // Execute all commands
  console.log(`📝 Setting ${commands.length} configuration variables...\n`);

  let successCount = 0;
  commands.forEach((command) => {
    if (executeFirebaseCommand(command)) {
      successCount++;
    }
  });

  console.log(
    `\n✅ Successfully set ${successCount}/${commands.length} configuration variables.`
  );

  if (successCount === commands.length) {
    console.log("\n🎉 Firebase Functions configuration setup complete!");
    console.log("\n📋 To view current configuration, run:");
    console.log("   firebase functions:config:get");
    console.log("\n🚀 You can now deploy your functions:");
    console.log("   firebase deploy --only functions");
  } else {
    console.log(
      "\n⚠️  Some configuration variables failed to set. Please check the errors above."
    );
    process.exit(1);
  }
}

// Run the setup
if (require.main === module) {
  setupFirebaseConfig();
}

module.exports = { setupFirebaseConfig, loadEnvFile };
