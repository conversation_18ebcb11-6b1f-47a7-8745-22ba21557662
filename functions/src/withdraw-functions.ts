import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import { TonClient, WalletContractV4, internal } from "@ton/ton";
import { mnemonicToPrivateKey } from "@ton/crypto";
import { UserEntity } from "./types";
import { hasAvailableBalance, spendLockedFunds } from "./balance-service";
import { applyWithdrawFee } from "./fee-service";
import { getTonRpcUrl, getMarketplaceWalletMnemonic } from "./config";

export const withdrawFunds = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError(
      "unauthenticated",
      "Authentication required."
    );
  }

  const { amount } = data;

  if (!amount || amount <= 0) {
    throw new functions.https.HttpsError(
      "invalid-argument",
      "Valid amount is required."
    );
  }

  try {
    const db = admin.firestore();
    const userId = context.auth.uid;

    // Get user data
    const userDoc = await db.collection("users").doc(userId).get();
    if (!userDoc.exists) {
      throw new functions.https.HttpsError("not-found", "User not found.");
    }

    const user = { id: userDoc.id, ...userDoc.data() } as UserEntity;

    // Check if user has a TON wallet address
    if (!user.ton_wallet_address) {
      throw new functions.https.HttpsError(
        "failed-precondition",
        "User does not have a TON wallet address configured."
      );
    }

    // Check if user has sufficient available balance
    const hasBalance = await hasAvailableBalance(userId, amount);
    if (!hasBalance) {
      throw new functions.https.HttpsError(
        "failed-precondition",
        "Insufficient available balance for withdrawal."
      );
    }

    // Apply withdrawal fee and get net amount
    const feeAmount = await applyWithdrawFee(userId, amount);
    const netAmountToUser = amount - feeAmount;

    if (netAmountToUser <= 0) {
      throw new functions.https.HttpsError(
        "failed-precondition",
        "Amount too small after fees."
      );
    }

    // Deduct the full amount from user's balance (including fee)
    await spendLockedFunds(userId, amount);

    // Initialize TON client
    const tonRpcUrl = getTonRpcUrl();
    const client = new TonClient({
      endpoint: tonRpcUrl,
    });

    // Get marketplace wallet mnemonic and create key pair
    const marketplaceMnemonic = getMarketplaceWalletMnemonic();
    const keyPair = await mnemonicToPrivateKey(marketplaceMnemonic.split(" "));

    // Create marketplace wallet contract
    const workchain = 0;
    const marketplaceWallet = WalletContractV4.create({
      workchain,
      publicKey: keyPair.publicKey,
    });
    const marketplaceContract = client.open(marketplaceWallet);

    // Get sequence number for the transaction
    const seqno = await marketplaceContract.getSeqno();

    // Convert amount to nanotons (1 TON = 1,000,000,000 nanotons)
    const amountInNanotons = Math.floor(netAmountToUser * 1000000000);

    // Create transfer transaction
    const transfer = await marketplaceContract.createTransfer({
      seqno,
      secretKey: keyPair.secretKey,
      messages: [
        internal({
          value: amountInNanotons.toString(),
          to: user.ton_wallet_address,
          body: "Withdrawal from marketplace",
        }),
      ],
    });

    // Send the transaction
    await marketplaceContract.send(transfer);

    console.log(
      `Withdrawal processed: ${netAmountToUser} TON sent to ${user.ton_wallet_address} (${feeAmount} TON fee applied)`
    );

    return {
      success: true,
      message: `Withdrawal successful. ${netAmountToUser} TON sent to your wallet (${feeAmount} TON fee applied)`,
      netAmount: netAmountToUser,
      feeAmount,
      transactionHash: transfer.hash().toString("hex"),
    };
  } catch (error) {
    console.error("Error processing withdrawal:", error);
    throw new functions.https.HttpsError(
      "internal",
      (error as any).message ?? "Server error while processing withdrawal."
    );
  }
});
