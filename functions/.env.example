# Environment Configuration
NODE_ENV=production

# Firebase Configuration
FIREBASE_PROJECT_ID=marketplace-362c0

# Firebase Service Account Key (optional, for custom token creation)
# FIREBASE_SERVICE_ACCOUNT_KEY={"type":"service_account","project_id":"..."}

# Telegram Configuration
TELEGRAM_BOT_TOKEN=**********************************************

# TON Configuration
TON_RPC_URL_TESTNET=https://testnet.toncenter.com/api/v2/
TON_RPC_URL_MAINNET=https://misty-crimson-friday.ton-mainnet.quiknode.pro/bd21cc29f2080adf31da2fe2af76f1f6df3d7b5c
TON_MARKETPLACE_WALLET=UQCWcm7v_aEFlLEUmAYuryEfpepeRcFVSVIimfmCYmxbE7gI
TON_NETWORK=mainnet

# TON Center API Key (optional, for better rate limits)
TONCENTER_API_KEY=your-api-key-here
