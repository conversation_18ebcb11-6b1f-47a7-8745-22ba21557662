"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Minus, Plus } from "lucide-react";
import Image from "next/image";

export default function MarketplaceHeader() {
  return (
    <header className="bg-ton-black text-white px-4 py-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3 bg-ton-black/60">
          <div className="flex items-center gap-2">
            <Image
              src="/ton.svg"
              alt="TON Logo"
              width={32}
              height={32}
              className="w-10 h-10"
            />
            <span className="text-xl font-bold">9546</span>
          </div>

          <div className="flex items-center gap-2">
            <Button
              size="icon"
              variant="default"
              className="w-10 h-10 rounded-full bg-ton-main hover:bg-ton-main/80 transition-all ease-in-out"
            >
              <Plus className="w-10 h-10 stroke-[2.5]" />
            </Button>
            <Button
              size="icon"
              variant="default"
              className="w-10 h-10 rounded-full bg-ton-main hover:bg-ton-main/80 transition-all ease-in-out"
            >
              <Minus className="w-10 h-10 stroke-[2.5]" />
            </Button>
          </div>

          <div className="w-10 h-10 rounded-full overflow-hidden bg-gray-300">
            <img
              src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face"
              alt="User avatar"
              className="w-full h-full object-cover"
            />
          </div>
        </div>

        <Button
          variant="default"
          size="lg"
          className="bg-ton-main hover:bg-ton-main hover:scale-110 transition-all ease-in-out text-white rounded-full text-lg font-semibold p-4"
        >
          <Image
            src="/ton.svg"
            alt="TON Logo"
            width={16}
            height={16}
            className="w-8 h-8"
          />
          Connect wallet
        </Button>
      </div>
    </header>
  );
}
