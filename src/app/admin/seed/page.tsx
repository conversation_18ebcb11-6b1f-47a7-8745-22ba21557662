"use client";

import { useState } from "react";
import { useRootContext } from "@/root-context";
import { Role } from "@/core.constants";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, Database, Trash2, Plus } from "lucide-react";
import { clearAllCollections, createBulkCollections } from "@/api/collection-api";
import { clearAllOrders, createBulkOrders } from "@/api/order-api";
import { generateSeedData } from "@/utils/seed-data";
import { toast } from "sonner";

export default function SeedPage() {
  const { role } = useRootContext();
  const [isLoading, setIsLoading] = useState(false);
  const [lastSeedResult, setLastSeedResult] = useState<{
    collectionsCreated: number;
    ordersCreated: number;
    timestamp: Date;
  } | null>(null);

  if (role !== Role.ADMIN) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Alert className="max-w-md">
          <AlertDescription>
            Access denied. Admin privileges required.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const handleReset = async () => {
    if (!confirm("Are you sure you want to reset the database? This will delete ALL collections and orders and create new seed data.")) {
      return;
    }

    setIsLoading(true);
    
    try {
      // Step 1: Clear existing data
      toast.info("Clearing existing collections and orders...");
      await Promise.all([
        clearAllCollections(),
        clearAllOrders()
      ]);

      // Step 2: Generate new seed data
      toast.info("Generating new seed data...");
      const { collections, orders } = generateSeedData();

      // Step 3: Create new data
      toast.info("Creating new collections and orders...");
      await Promise.all([
        createBulkCollections(collections),
        createBulkOrders(orders)
      ]);

      // Update result
      setLastSeedResult({
        collectionsCreated: collections.length,
        ordersCreated: orders.length,
        timestamp: new Date()
      });

      toast.success(`Successfully reset database! Created ${collections.length} collections and ${orders.length} orders.`);
    } catch (error) {
      console.error("Error resetting database:", error);
      toast.error("Failed to reset database. Please check the console for details.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold">Database Seed Management</h1>
          <p className="text-muted-foreground">
            Reset and populate the database with test data
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Seed Configuration
            </CardTitle>
            <CardDescription>
              This will create test data for development and testing purposes
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h3 className="font-semibold">Collections</h3>
                <p className="text-sm text-muted-foreground">
                  4 random collections will be created with different statuses and metadata
                </p>
              </div>
              <div className="space-y-2">
                <h3 className="font-semibold">Orders</h3>
                <p className="text-sm text-muted-foreground">
                  20 random orders will be created from the 2 specified user accounts
                </p>
              </div>
            </div>

            <Alert>
              <AlertDescription>
                <strong>User IDs that will be used:</strong>
                <br />
                • nA8hZACyy4MKpSuh4h6sTpD0OKl2
                <br />
                • P9Kl4VRZtuRAmt8FQfUIgPUl1A43
              </AlertDescription>
            </Alert>

            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                onClick={handleReset}
                disabled={isLoading}
                className="flex items-center gap-2"
                variant="destructive"
                size="lg"
              >
                {isLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Trash2 className="h-4 w-4" />
                )}
                {isLoading ? "Resetting..." : "Reset Database"}
              </Button>
            </div>
          </CardContent>
        </Card>

        {lastSeedResult && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-green-600">
                <Plus className="h-5 w-5" />
                Last Seed Operation
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-blue-600">
                    {lastSeedResult.collectionsCreated}
                  </div>
                  <div className="text-sm text-muted-foreground">Collections Created</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">
                    {lastSeedResult.ordersCreated}
                  </div>
                  <div className="text-sm text-muted-foreground">Orders Created</div>
                </div>
                <div>
                  <div className="text-sm font-medium">
                    {lastSeedResult.timestamp.toLocaleString()}
                  </div>
                  <div className="text-sm text-muted-foreground">Completed At</div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <Card>
          <CardHeader>
            <CardTitle>⚠️ Warning</CardTitle>
          </CardHeader>
          <CardContent>
            <Alert variant="destructive">
              <AlertDescription>
                This operation will permanently delete ALL existing collections and orders 
                from the database. This action cannot be undone. Only use this in development 
                or testing environments.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
