"use client";

import { useEffect } from "react";

import { Role } from "@/core.constants";
import { useRootContext } from "@/root-context";
import { CollectionManagement } from "./collection-management";
import { FeesManagement } from "./fees-management";

export default function Admin() {
  const { role } = useRootContext();

  useEffect(() => {
    document.documentElement.classList.add("dark");
  }, []);

  if (role !== Role.ADMIN) return null;

  return (
    <div className="mx-auto w-full max-w-6xl p-4 space-y-6">
      {/* <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Admin Panel</h1>
        <p className="text-muted-foreground">
          Manage your marketplace collections, orders, and database
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Collection Management
            </CardTitle>
            <CardDescription>
              Create, edit, and manage marketplace collections
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              Manage all collections in your marketplace including their status,
              metadata, and images.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Database Seed
            </CardTitle>
            <CardDescription>
              Reset and populate database with test data
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              Clear all collections and orders, then create 4 random collections
              and 20 random orders for testing.
            </p>
            <Link href="/admin/seed">
              <Button className="w-full">Go to Seed Management</Button>
            </Link>
          </CardContent>
        </Card>
      </div> */}
      <FeesManagement />

      <CollectionManagement />
    </div>
  );
}
