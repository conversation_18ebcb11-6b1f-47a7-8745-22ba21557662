import { doc, getDoc, setDoc } from "firebase/firestore";
import { firestore } from "@/root-context";

export interface AppConfig {
  depositFee: number;
  withdrawFee: number;
  referrer_fee: number;
  reject_order_fee: number;
  purchase_fee: number;
}

const APP_CONFIG_COLLECTION = "app_config";
const APP_CONFIG_DOC_ID = "fees";

export const loadFeesConfig = async (): Promise<AppConfig | null> => {
  try {
    const configDoc = await getDoc(
      doc(firestore, APP_CONFIG_COLLECTION, APP_CONFIG_DOC_ID)
    );

    if (configDoc.exists()) {
      return configDoc.data() as AppConfig;
    }

    return null;
  } catch (error) {
    console.error("Error loading fees config:", error);
    throw error;
  }
};

export const updateFeesConfig = async (config: AppConfig): Promise<void> => {
  try {
    await setDoc(
      doc(firestore, APP_CONFIG_COLLECTION, APP_CONFIG_DOC_ID),
      config
    );
  } catch (error) {
    console.error("Error updating fees config:", error);
    throw error;
  }
};
