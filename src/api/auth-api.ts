import { GoogleAuthProvider, signInWithPopup } from "firebase/auth";
import { collection, getDocs, query, where } from "firebase/firestore";

import { firebaseAuth, firestore } from "@/root-context";

export const signInWithGoogle = async () => {
  const provider = new GoogleAuthProvider();

  try {
    await signInWithPopup(firebaseAuth, provider);
  } catch (error) {
    console.error("Error during sign in:", error);
  }
};

export const signOut = async () => {
  try {
    await firebaseAuth.signOut();
  } catch (error) {
    console.error("Error during sign out:", error);
  }
};

export const getCurrentUserId = () => firebaseAuth.currentUser?.uid;

// TODO: Reimplement with new SDK approach
export const signInWithTelegram = async () => {
  // This function needs to be rewritten to work with the new @telegram-apps/sdk-react approach
  throw new Error(
    "signInWithTelegram is temporarily disabled - use TelegramAuth component instead"
  );
};

export const getUserRole = async () => {
  try {
    const q = query(
      collection(firestore, "users"),
      where("id", "==", getCurrentUserId())
    );

    const adminSnapshot = await getDocs(q);
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const admins: any[] = [];
    adminSnapshot.forEach((doc) => {
      admins.push({ id: doc.id, ...doc.data() });
    });
    return admins[0]?.role;
  } catch (error) {
    console.error("Error checking if current User is admin:", error);
    throw error;
  }
};
