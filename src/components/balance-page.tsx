'use client'

import { useState, useEffect, useCallback } from 'react';
import { UserEntity, UserBalance } from '@/core.constants';
import BalanceDisplay from './balance-display';
import BalanceTopup from './balance-topup';

interface BalancePageProps {
  user: UserEntity;
}

export default function BalancePage({ user }: BalancePageProps) {
  const [balance, setBalance] = useState<UserBalance | undefined>(user.balance);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');

  // Fetch current balance from Firebase
  const fetchBalance = useCallback(async () => {
    setIsLoading(true);
    setError('');

    try {
      // This would call your Firebase function
      // const result = await functions().httpsCallable('getBalance')();
      // setBalance(result.data.balance);
      
      // For now, use the balance from user prop
      setBalance(user.balance || { sum: 0, locked: 0 });
    } catch (error) {
      console.error('Error fetching balance:', error);
      setError('Failed to fetch balance');
    } finally {
      setIsLoading(false);
    }
  }, [user.balance]);

  useEffect(() => {
    fetchBalance();
  }, [fetchBalance]);

  const handleTopupSuccess = useCallback((amount: number) => {
    // Optimistically update balance
    setBalance(prev => prev ? {
      sum: prev.sum + amount,
      locked: prev.locked
    } : { sum: amount, locked: 0 });

    // Refresh balance from server after a delay
    setTimeout(() => {
      fetchBalance();
    }, 2000);
  }, [fetchBalance]);

  const refreshBalance = useCallback(() => {
    fetchBalance();
  }, [fetchBalance]);

  if (isLoading && !balance) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Wallet Balance</h1>
        <button
          onClick={refreshBalance}
          disabled={isLoading}
          className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-50"
        >
          {isLoading ? 'Refreshing...' : 'Refresh'}
        </button>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600">{error}</p>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Balance Display */}
        <div className="space-y-4">
          <BalanceDisplay balance={balance} />
          
          {/* Transaction History Placeholder */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Recent Transactions
            </h3>
            <div className="text-center py-8 text-gray-500">
              <p>Transaction history will be displayed here</p>
              <p className="text-sm mt-2">
                Top-ups and purchases will appear in this section
              </p>
            </div>
          </div>
        </div>

        {/* Top-up Section */}
        <div className="space-y-4">
          <BalanceTopup user={user} onTopupSuccess={handleTopupSuccess} />
          
          {/* Balance Info */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="text-md font-semibold text-blue-900 mb-2">
              How Balance Works
            </h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Top up your balance by sending TON to the marketplace</li>
              <li>• Funds are automatically credited after blockchain confirmation</li>
              <li>• Locked funds are reserved for pending orders</li>
              <li>• Available balance can be used for new purchases</li>
              <li>• All transactions are recorded on the TON blockchain</li>
            </ul>
          </div>

          {/* Security Notice */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h4 className="text-md font-semibold text-yellow-900 mb-2">
              Security Notice
            </h4>
            <ul className="text-sm text-yellow-800 space-y-1">
              <li>• Only send TON from your connected wallet</li>
              <li>• Transactions from other wallets won't be credited</li>
              <li>• Keep your wallet secure and never share private keys</li>
              <li>• Contact support if you have any issues</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Balance Breakdown */}
      {balance && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Balance Breakdown
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <p className="text-2xl font-bold text-green-600">
                {balance.sum.toFixed(2)}
              </p>
              <p className="text-sm text-green-700">Total Balance</p>
            </div>
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <p className="text-2xl font-bold text-blue-600">
                {(balance.sum - balance.locked).toFixed(2)}
              </p>
              <p className="text-sm text-blue-700">Available</p>
            </div>
            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <p className="text-2xl font-bold text-orange-600">
                {balance.locked.toFixed(2)}
              </p>
              <p className="text-sm text-orange-700">Locked</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
