"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Loader2, Download, AlertCircle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface TelegramFileViewerProps {
  fileId: string;
}

export default function TelegramFileViewer({
  fileId,
}: TelegramFileViewerProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [fileUrl, setFileUrl] = useState<string | null>(null);
  const [fileInfo, setFileInfo] = useState<{
    contentType: string;
    size: number;
  } | null>(null);
  const [apiType, setApiType] = useState<"bot" | "mtproto">("bot");

  const fetchFile = async () => {
    setLoading(true);
    setError(null);
    setFileUrl(null);
    setFileInfo(null);

    try {
      const endpoint =
        apiType === "bot"
          ? `/api/telegram/get-file?fileId=${encodeURIComponent(fileId)}`
          : `/api/telegram/get-mtproto-file?documentId=${encodeURIComponent(
              fileId
            )}`;

      const response = await fetch(endpoint);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch file");
      }

      // Get file info from headers
      const contentType =
        response.headers.get("content-type") || "application/octet-stream";
      const contentLength = response.headers.get("content-length");
      const size = contentLength ? parseInt(contentLength, 10) : 0;

      setFileInfo({ contentType, size });

      // Create blob URL for the file
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      setFileUrl(url);
    } catch (err) {
      console.error("Error fetching file:", err);
      setError(err instanceof Error ? err.message : "Unknown error occurred");
    } finally {
      setLoading(false);
    }
  };

  const downloadFile = () => {
    if (fileUrl) {
      const link = document.createElement("a");
      link.href = fileUrl;
      link.download = `telegram-file-${fileId}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // Cleanup blob URL on unmount
  useEffect(() => {
    return () => {
      if (fileUrl) {
        URL.revokeObjectURL(fileUrl);
      }
    };
  }, [fileUrl]);

  return (
    <Card className="w-full max-w-2xl mx-auto bg-slate-800 border-slate-700">
      <CardHeader>
        <CardTitle className="text-white">Telegram File Viewer</CardTitle>
        <CardDescription className="text-slate-400">
          File ID: {fileId}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-3">
          <div className="flex gap-2">
            <Button
              onClick={() => setApiType("bot")}
              variant={apiType === "bot" ? "default" : "outline"}
              size="sm"
              className={
                apiType === "bot"
                  ? "bg-blue-600 hover:bg-blue-700"
                  : "border-slate-600 text-slate-300 hover:bg-slate-700"
              }
            >
              Bot API
            </Button>
            <Button
              onClick={() => setApiType("mtproto")}
              variant={apiType === "mtproto" ? "default" : "outline"}
              size="sm"
              className={
                apiType === "mtproto"
                  ? "bg-blue-600 hover:bg-blue-700"
                  : "border-slate-600 text-slate-300 hover:bg-slate-700"
              }
            >
              MTProto API
            </Button>
          </div>

          <div className="flex gap-2">
            <Button
              onClick={fetchFile}
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Loading...
                </>
              ) : (
                `Fetch File (${apiType.toUpperCase()})`
              )}
            </Button>

            {fileUrl && (
              <Button
                onClick={downloadFile}
                variant="outline"
                className="border-slate-600 text-slate-300 hover:bg-slate-700"
              >
                <Download className="mr-2 h-4 w-4" />
                Download
              </Button>
            )}
          </div>
        </div>

        {error && (
          <Alert className="border-red-600 bg-red-900/20">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="text-red-400">
              {error}
            </AlertDescription>
          </Alert>
        )}

        {fileInfo && (
          <div className="text-sm text-slate-400 space-y-1">
            <p>Content Type: {fileInfo.contentType}</p>
            <p>Size: {formatFileSize(fileInfo.size)}</p>
          </div>
        )}

        {fileUrl && fileInfo?.contentType.startsWith("image/") && (
          <div className="border border-slate-600 rounded-lg overflow-hidden">
            <img
              src={fileUrl}
              alt={`Telegram file ${fileId}`}
              className="w-full h-auto max-h-96 object-contain bg-slate-900"
            />
          </div>
        )}

        {fileUrl && !fileInfo?.contentType.startsWith("image/") && (
          <div className="p-4 border border-slate-600 rounded-lg bg-slate-900">
            <p className="text-slate-300 text-center">
              File loaded successfully. Use the download button to save it.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
