"use client";

import { useEffect, useState } from "react";
import <PERSON><PERSON> from "lottie-react";

interface TgsLottieProps {
  src: string; // Path to the JSON file (converted from TGS)
  className?: string;
  width?: number;
  height?: number;
  loop?: boolean;
  autoplay?: boolean;
  speed?: number;
}

export default function TgsLottie({
  src,
  className = "",
  width = 200,
  height = 200,
  loop = true,
  autoplay = true,
  speed = 1,
}: TgsLottieProps) {
  const [animationData, setAnimationData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadAnimation = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Fetch the JSON file
        const response = await fetch(src);
        
        if (!response.ok) {
          throw new Error(`Failed to load animation: ${response.status}`);
        }
        
        // Parse the JSON data
        const jsonData = await response.json();
        setAnimationData(jsonData);
      } catch (err) {
        console.error("Error loading animation:", err);
        setError(err instanceof Error ? err.message : "Failed to load animation");
      } finally {
        setLoading(false);
      }
    };

    loadAnimation();
  }, [src]);

  if (loading) {
    return (
      <div 
        className={`flex items-center justify-center ${className}`}
        style={{ width, height }}
      >
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div 
        className={`flex items-center justify-center bg-gray-100 rounded-lg ${className}`}
        style={{ width, height }}
      >
        <div className="text-center p-4">
          <div className="text-red-500 text-sm mb-2">⚠️</div>
          <div className="text-xs text-gray-600">{error}</div>
        </div>
      </div>
    );
  }

  if (!animationData) {
    return (
      <div 
        className={`flex items-center justify-center bg-gray-100 rounded-lg ${className}`}
        style={{ width, height }}
      >
        <div className="text-gray-500 text-sm">No animation data</div>
      </div>
    );
  }

  return (
    <div className={className} style={{ width, height }}>
      <Lottie
        animationData={animationData}
        loop={loop}
        autoplay={autoplay}
        speed={speed}
        style={{ width: "100%", height: "100%" }}
      />
    </div>
  );
}
