import axios from 'axios';
import dotenv from 'dotenv';

dotenv.config();

const FIREBASE_PROJECT_ID = process.env.FIREBASE_PROJECT_ID;
const FIREBASE_REGION = process.env.FIREBASE_REGION || 'us-central1';
const BOT_TOKEN = process.env.BOT_TOKEN;

if (!FIREBASE_PROJECT_ID) {
  throw new Error('FIREBASE_PROJECT_ID is required in environment variables');
}

if (!BOT_TOKEN) {
  throw new Error('BOT_TOKEN is required in environment variables');
}

const FIREBASE_FUNCTIONS_BASE_URL = `https://${FIREBASE_REGION}-${FIREBASE_PROJECT_ID}.cloudfunctions.net`;

export interface OrderEntity {
  id: string;
  number: number;
  buyerId?: string;
  sellerId: string;
  productId: string;
  amount: number;
  status: 'active' | 'paid' | 'fulfilled' | 'cancelled';
  createdAt: any;
  updatedAt: any;
}

export interface GetUserOrdersResponse {
  success: boolean;
  orders: OrderEntity[];
  count: number;
  userId: string;
}

export interface CompletePurchaseResponse {
  success: boolean;
  message: string;
  netAmountToSeller: number;
  feeAmount: number;
  order: {
    id: string;
    number: number;
    status: string;
  };
}

/**
 * Get user orders by Telegram ID
 */
export async function getUserOrdersByTgId(tgId: string): Promise<GetUserOrdersResponse> {
  try {
    const response = await axios.post(
      `${FIREBASE_FUNCTIONS_BASE_URL}/getUserOrders`,
      {
        data: {
          tgId: tgId,
        },
      },
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    return response.data.result;
  } catch (error) {
    console.error('Error getting user orders:', error);
    throw new Error('Failed to get user orders');
  }
}

/**
 * Complete purchase by bot
 */
export async function completePurchaseByBot(orderId: string): Promise<CompletePurchaseResponse> {
  try {
    const response = await axios.post(
      `${FIREBASE_FUNCTIONS_BASE_URL}/completePurchaseByBot`,
      {
        data: {
          orderId: orderId,
          botToken: BOT_TOKEN,
        },
      },
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    return response.data.result;
  } catch (error) {
    console.error('Error completing purchase by bot:', error);
    if (axios.isAxiosError(error) && error.response) {
      const errorMessage = error.response.data?.error?.message || 'Failed to complete purchase';
      throw new Error(errorMessage);
    }
    throw new Error('Failed to complete purchase');
  }
}

/**
 * Format order for display
 */
export function formatOrderForDisplay(order: OrderEntity): string {
  const statusEmoji = {
    active: '🟡',
    paid: '🟠',
    fulfilled: '✅',
    cancelled: '❌',
  };

  const roleText = order.buyerId ? 'Buyer' : 'Seller';
  const emoji = statusEmoji[order.status] || '⚪';
  
  return `${emoji} Order #${order.number}\n` +
         `Role: ${roleText}\n` +
         `Amount: ${order.amount} TON\n` +
         `Status: ${order.status.toUpperCase()}`;
}

/**
 * Get orders that are ready for completion (status: paid)
 */
export function getCompletableOrders(orders: OrderEntity[]): OrderEntity[] {
  return orders.filter(order => order.status === 'paid' && order.buyerId);
}
