import {
  Collection,
  CollectionStatus,
  OrderEntity,
  OrderStatus,
} from "@/core.constants";

type CollectionInput = Omit<Collection, "id" | "createdAt" | "updatedAt">;
type OrderInput = Omit<OrderEntity, "id" | "createdAt" | "updatedAt">;

const USER_IDS = [
  "nA8hZACyy4MKpSuh4h6sTpD0OKl2",
  "P9Kl4VRZtuRAmt8FQfUIgPUl1A43",
];

const COLLECTION_NAMES = [
  "Cosmic Cats",
  "Digital Dragons",
  "Pixel Pirates",
  "Neon Ninjas",
  "Cyber Samurai",
  "Space Explorers",
  "Mystic Monsters",
  "Robot Warriors",
  "Fantasy Fighters",
  "Ocean Legends",
];

const COLLECTION_DESCRIPTIONS = [
  "A unique collection of cosmic feline creatures exploring the universe",
  "Legendary dragons from the digital realm with mystical powers",
  "Swashbuckling pirates sailing the pixelated seas",
  "Stealthy warriors from the neon-lit future",
  "Ancient samurai enhanced with cybernetic technology",
  "Brave explorers venturing into the depths of space",
  "Mysterious creatures from forgotten magical realms",
  "Mechanical warriors built for epic battles",
  "Heroic fighters from fantastical worlds",
  "Legendary beings from the deepest oceans",
];

const LOGO_URLS = [
  "https://via.placeholder.com/200x200/FF6B6B/FFFFFF?text=CC",
  "https://via.placeholder.com/200x200/4ECDC4/FFFFFF?text=DD",
  "https://via.placeholder.com/200x200/45B7D1/FFFFFF?text=PP",
  "https://via.placeholder.com/200x200/96CEB4/FFFFFF?text=NN",
  "https://via.placeholder.com/200x200/FFEAA7/000000?text=CS",
  "https://via.placeholder.com/200x200/DDA0DD/FFFFFF?text=SE",
  "https://via.placeholder.com/200x200/98D8C8/FFFFFF?text=MM",
  "https://via.placeholder.com/200x200/F7DC6F/000000?text=RW",
  "https://via.placeholder.com/200x200/BB8FCE/FFFFFF?text=FF",
  "https://via.placeholder.com/200x200/85C1E9/FFFFFF?text=OL",
];

function getRandomElement<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

function getRandomPrice(): number {
  // Generate random price between 0.1 and 10 TON
  return Math.round((Math.random() * 9.9 + 0.1) * 100) / 100;
}

function getRandomCollectionId(): string {
  return `collection_${Math.random().toString(36).substring(2, 11)}`;
}

function getRandomItemId(): string {
  return `item_${Math.random().toString(36).substring(2, 11)}`;
}

export function generateRandomCollections(
  count: number = 4
): CollectionInput[] {
  const collections: CollectionInput[] = [];

  for (let i = 0; i < count; i++) {
    const nameIndex = i % COLLECTION_NAMES.length;
    collections.push({
      collectionId: getRandomCollectionId(),
      name: COLLECTION_NAMES[nameIndex],
      logoUrl: LOGO_URLS[nameIndex],
      description: COLLECTION_DESCRIPTIONS[nameIndex],
      status: getRandomElement([
        CollectionStatus.ACTIVE,
        CollectionStatus.PRELAUNCH,
      ]),
    });
  }

  return collections;
}

export function generateRandomOrders(count: number = 20): OrderInput[] {
  const orders: OrderInput[] = [];

  // Note: Orders now use productId instead of collection_id

  for (let i = 0; i < count; i++) {
    const sellerId = getRandomElement(USER_IDS);
    const buyerId =
      Math.random() > 0.3
        ? getRandomElement(USER_IDS.filter((id) => id !== sellerId))
        : undefined;

    orders.push({
      sellerId,
      productId: getRandomItemId(),
      amount: getRandomPrice(),
      buyerId,
      status: buyerId
        ? getRandomElement([
            OrderStatus.PAID,
            OrderStatus.FULFILLED,
            OrderStatus.ACTIVE,
          ])
        : OrderStatus.ACTIVE,
    });
  }

  return orders;
}

export function generateSeedData() {
  const collections = generateRandomCollections(4);
  const orders = generateRandomOrders(20);

  return {
    collections,
    orders,
  };
}
