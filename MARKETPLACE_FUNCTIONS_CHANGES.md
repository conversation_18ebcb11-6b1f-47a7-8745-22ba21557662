# Marketplace Functions Changes - Fee System Implementation

## Overview

This document outlines the comprehensive changes made to implement a new fee system and order management workflow for the marketplace functions.

## 1. New App Config Collection

### Created `app_config` Firestore Collection
- **Collection**: `app_config`
- **Document ID**: `fees`
- **Structure**:
  ```typescript
  interface AppConfig {
    depositFee: number;      // in BPS (basis points)
    withdrawFee: number;     // in BPS
    referrer_fee: number;    // in BPS
    reject_order_fee: number; // in BPS
    purchase_fee: number;    // in BPS
  }
  ```

### Default Fee Values (in BPS)
- **Deposit Fee**: 100 BPS (1%)
- **Withdraw Fee**: 50 BPS (0.5%)
- **Referrer Fee**: 200 BPS (2%)
- **Reject Order Fee**: 150 BPS (1.5%)
- **Purchase Fee**: 75 BPS (0.75%)

## 2. New Fee Service Module

### Created `functions/src/fee-service.ts`
- **`getAppConfig()`**: Retrieves fee configuration from Firestore
- **`calculateFeeAmount()`**: Calculates fee amount from BPS
- **`getAdminUser()`**: Finds admin user for fee collection
- **`applyFeeToAdmin()`**: Adds fee amounts to admin account
- **`applyDepositFee()`**: Applies deposit fees during transaction processing
- **`applyRejectOrderFee()`**: Applies rejection fees when orders are cancelled
- **`applyPurchaseFee()`**: Applies purchase fees during order completion

## 3. Transaction Monitoring Updates

### Modified `functions/src/ton-monitor.ts`
- **Added 0.9 TON Filter**: Only processes transactions > 0.9 TON
- **Integrated Deposit Fees**: Automatically applies deposit fees before crediting user balances
- **Fee Collection**: Deposit fees are automatically added to admin account balance

### Changes Made:
```typescript
// Filter transactions by amount
if (amount <= 0.9) {
  console.log(`Transaction amount ${amount} TON is below 0.9 TON threshold, skipping`);
  return null;
}

// Apply deposit fee before crediting user
const netAmount = await applyDepositFee(userId, amount);
await addFunds(userId, netAmount);
```

## 4. Removed Deprecated Functions

### Deleted from `functions/src/balance-functions.ts`:
- `checkPurchaseEligibility`
- `lockOrderFunds`
- `unlockOrderFunds`
- `completeOrder`

### Cleaned up imports and exports accordingly

## 5. New Order Management Functions

### Created `functions/src/order-functions.ts`

#### `createOrder`
- **Purpose**: Creates new orders with seller authentication and fund locking
- **Validation**: 
  - Seller auth ID must match payload seller ID
  - Checks seller's available balance
  - Locks seller's funds upon order creation
- **Order Status**: Sets initial status to "active"

#### `makePurchase`
- **Purpose**: Allows buyers to purchase active orders
- **Validation**:
  - Buyer auth ID validation
  - Sufficient buyer balance check
  - Prevents self-purchase
- **Process**:
  - Locks buyer's funds
  - Applies purchase fee
  - Updates order status to "paid"
  - Adds buyer ID to order

#### `rejectPurchase`
- **Purpose**: Allows buyer or seller to reject orders
- **Validation**:
  - Only buyer or seller can reject
  - Order must be in "active" or "paid" status
- **Process**:
  - Applies rejection fee to rejector
  - Unlocks rejector's funds
  - Unlocks other party's funds if applicable
  - Sets order status to "cancelled"

## 6. Updated Order Types

### Backend (`functions/src/types.ts`):
```typescript
interface OrderEntity {
  id: string;
  buyerId?: string; // Optional - orders can exist without buyers
  sellerId: string;
  productId: string;
  amount: number;
  status: "active" | "paid" | "fulfilled" | "cancelled";
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

### Frontend (`src/core.constants.ts`):
```typescript
enum OrderStatus {
  ACTIVE = "active",
  PAID = "paid", 
  FULFILLED = "fulfilled",
  CANCELLED = "cancelled",
}

interface OrderEntity {
  id?: string;
  buyerId?: string;
  sellerId: string;
  productId: string;
  amount: number;
  status: OrderStatus;
  createdAt?: Date;
  updatedAt?: Date;
}
```

## 7. New Cloud Functions Exports

### Updated `functions/src/index.ts`:
```typescript
// Removed deprecated exports
export { getBalance } from "./balance-functions";

// Added new order management functions
export {
  createOrder,
  makePurchase,
  rejectPurchase,
} from "./order-functions";

// Added app config initialization
export { initAppConfig } from "./init-app-config";
```

## 8. App Config Initialization

### Created `functions/src/init-app-config.ts`
- **`initAppConfig`**: Cloud function to initialize fee configuration
- **Admin Only**: Restricted to admin users
- **One-time Setup**: Sets up default fee values in Firestore

## 9. Fee Logic Summary

### Deposit Fees
- Applied when users send TON to marketplace wallet
- Only for transactions > 0.9 TON
- Fee deducted before crediting user balance
- Fee amount added to admin account

### Purchase Fees
- Applied when buyers complete purchases
- Deducted from buyer's balance
- Added to admin account

### Rejection Fees
- Applied to whoever rejects an order (buyer or seller)
- Deducted from rejector's balance
- Added to admin account
- Funds unlocked for both parties

### Fee Calculation
- All fees stored in BPS (basis points)
- 1 BPS = 0.01%
- 100 BPS = 1%
- Formula: `feeAmount = (amount * feeBps) / 10000`

## 10. Migration Notes

### For Existing Orders
- Old order structure will need migration to new format
- Status mapping: "pending" → "active", "confirmed" → "paid", "completed" → "fulfilled"

### For Frontend
- Update order creation to use new `createOrder` function
- Update purchase flow to use `makePurchase` function
- Add rejection functionality using `rejectPurchase` function
- Update order status displays to match new enum values

## 11. Testing Recommendations

1. **Initialize App Config**: Call `initAppConfig` function once as admin
2. **Test Deposit Flow**: Send > 0.9 TON to marketplace wallet and verify fee deduction
3. **Test Order Creation**: Create orders and verify fund locking
4. **Test Purchase Flow**: Complete purchases and verify fee application
5. **Test Rejection Flow**: Reject orders and verify fee application and fund unlocking
6. **Verify Admin Balance**: Check that all fees are properly credited to admin account

## 12. Environment Setup

Ensure the following are configured:
- Firebase Firestore rules allow read/write to `app_config` collection
- Admin user exists in `users` collection with `role: "admin"`
- TON transaction monitoring is active
- Marketplace wallet address is properly configured
