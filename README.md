# Marketplace Telegram Bot

A TypeScript-based Telegram bot for the marketplace platform that provides users with easy access to the marketplace web app and order management features.

## Features

- 🌐 **Web App Integration**: Menu button that opens the marketplace web app
- 👋 **Hello World**: Simple greeting functionality
- ✅ **Order Management**: Help users complete their orders
- 📞 **Support Integration**: Easy access to customer support
- 🔧 **TypeScript**: Fully typed codebase for better development experience

## Setup

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- A Telegram <PERSON><PERSON> (get one from [@BotFather](https://t.me/BotFather))

### Installation

1. **Clone and navigate to the bot directory**:
   ```bash
   cd marketplace-bot
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Configure environment variables**:
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` and fill in your configuration:
   ```env
   BOT_TOKEN=your_telegram_bot_token_here
   BOT_USERNAME=your_bot_username_here
   WEB_APP_URL=https://4d5rqhd0-3000.euw.devtunnels.ms/
   ```

4. **Build the project**:
   ```bash
   npm run build
   ```

## Development

### Running in Development Mode

```bash
npm run dev
```

This will start the bot with hot reloading using nodemon.

### Building for Production

```bash
npm run build
npm start
```

### Available Scripts

- `npm run dev` - Start development server with hot reloading
- `npm run build` - Build TypeScript to JavaScript
- `npm run build:watch` - Build in watch mode
- `npm start` - Start the production server
- `npm run clean` - Remove build directory

## Bot Commands

- `/start` - Initialize the bot and show the main menu
- `/help` - Show help information

## Bot Buttons

- **👋 Hello World** - Display a welcome message
- **✅ Complete Order** - Get help with completing orders
- **🌐 Open Marketplace** - Direct link to the web app (also available as menu button)

## Configuration

### Environment Variables

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `BOT_TOKEN` | Telegram Bot Token from BotFather | Yes | - |
| `BOT_USERNAME` | Bot username (without @) | No | - |
| `WEB_APP_URL` | URL of the marketplace web app | Yes | - |
| `PORT` | Server port for webhook mode | No | 3001 |
| `NODE_ENV` | Environment (development/production) | No | development |
| `WEBHOOK_URL` | Webhook URL for production | No | - |

### Deployment Modes

#### Development (Polling)
The bot uses polling to receive updates. This is suitable for development and testing.

#### Production (Webhook)
Set `NODE_ENV=production` and `WEBHOOK_URL` to use webhook mode for better performance in production.

## Project Structure

```
marketplace-bot/
├── src/
│   ├── bot.ts          # Main bot logic and handlers
│   └── index.ts        # Entry point and server setup
├── dist/               # Compiled JavaScript (generated)
├── .env                # Environment variables (not in git)
├── .env.example        # Environment variables template
├── package.json        # Dependencies and scripts
├── tsconfig.json       # TypeScript configuration
├── nodemon.json        # Nodemon configuration
└── README.md          # This file
```

## Getting Your Bot Token

1. Open Telegram and search for [@BotFather](https://t.me/BotFather)
2. Send `/newbot` command
3. Follow the instructions to create your bot
4. Copy the bot token and add it to your `.env` file
5. Optionally, set up a menu button using `/setmenubutton` command in BotFather

## Troubleshooting

### Common Issues

1. **Bot not responding**: Check if the bot token is correct and the bot is not already running elsewhere
2. **Web app not opening**: Verify the `WEB_APP_URL` is accessible and correct
3. **Build errors**: Make sure all dependencies are installed with `npm install`

### Logs

The bot logs important events to the console. In development mode, you'll see detailed logs including:
- Bot startup status
- Menu button configuration
- Error messages
- User interactions

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

ISC License - see package.json for details
