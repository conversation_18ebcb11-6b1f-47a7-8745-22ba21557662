#!/bin/bash

# Script to convert TGS files to JSON for use with <PERSON><PERSON>
# Usage: ./scripts/convert-tgs.sh input.tgs output.json

if [ $# -ne 2 ]; then
    echo "Usage: $0 <input.tgs> <output.json>"
    echo "Example: $0 animation.tgs animation.json"
    exit 1
fi

INPUT_FILE="$1"
OUTPUT_FILE="$2"

# Check if input file exists
if [ ! -f "$INPUT_FILE" ]; then
    echo "Error: Input file '$INPUT_FILE' not found"
    exit 1
fi

# Check if input file is actually gzipped
if ! file "$INPUT_FILE" | grep -q "gzip compressed"; then
    echo "Error: '$INPUT_FILE' is not a gzipped file (TGS files should be gzipped JSON)"
    exit 1
fi

# Convert TGS to JSON
echo "Converting $INPUT_FILE to $OUTPUT_FILE..."
gunzip -c "$INPUT_FILE" > "$OUTPUT_FILE"

if [ $? -eq 0 ]; then
    echo "✅ Successfully converted $INPUT_FILE to $OUTPUT_FILE"
    echo "📁 File size: $(du -h "$OUTPUT_FILE" | cut -f1)"
else
    echo "❌ Failed to convert $INPUT_FILE"
    exit 1
fi
