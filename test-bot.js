// Simple test script to verify bot configuration
// Run this with: node test-bot.js

require('dotenv').config();

const BOT_TOKEN = process.env.BOT_TOKEN;
const WEB_APP_URL = process.env.WEB_APP_URL;

console.log('🤖 Testing Bot Configuration...\n');

// Check required environment variables
if (!BOT_TOKEN) {
  console.error('❌ BOT_TOKEN is missing in .env file');
  console.log('   Please add your bot token from @BotFather');
  process.exit(1);
}

if (!WEB_APP_URL) {
  console.error('❌ WEB_APP_URL is missing in .env file');
  console.log('   Please add your web app URL');
  process.exit(1);
}

console.log('✅ BOT_TOKEN is configured');
console.log('✅ WEB_APP_URL is configured:', WEB_APP_URL);

// Test bot token format
if (!BOT_TOKEN.match(/^\d+:[A-Za-z0-9_-]+$/)) {
  console.warn('⚠️  BOT_TOKEN format looks incorrect');
  console.log('   Expected format: 1234567890:ABCdefGHIjklMNOpqrsTUVwxyz');
} else {
  console.log('✅ BOT_TOKEN format looks correct');
}

// Test URL format
try {
  new URL(WEB_APP_URL);
  console.log('✅ WEB_APP_URL format is valid');
} catch (error) {
  console.error('❌ WEB_APP_URL format is invalid:', error.message);
}

console.log('\n🚀 Configuration test completed!');
console.log('\nNext steps:');
console.log('1. Run "npm run dev" to start the bot in development mode');
console.log('2. Send /start to your bot in Telegram');
console.log('3. Test the menu button and inline buttons');
