# TGS Lottie Animation Usage

This document explains how to use TGS (Telegram Sticker) files as Lottie animations in the marketplace UI.

## Overview

TGS files are gzipped JSON files that contain Lottie animation data. To use them in React components, they need to be decompressed first.

## Components

### TgsLottie

A generic component for rendering any TGS animation (converted to JSON).

```tsx
import TgsLottie from "@/components/TgsLottie";

function MyComponent() {
  return (
    <TgsLottie
      src="/path/to/animation.json"
      width={300}
      height={300}
      loop={true}
      autoplay={true}
      speed={1}
      className="rounded-lg"
    />
  );
}
```

### BackyardLottie

A specific component for the Backyard animation used in the marketplace.

```tsx
import BackyardLottie from "@/components/BackyardLottie";

function MarketplacePage() {
  return (
    <div>
      <BackyardLottie 
        width={300} 
        height={300} 
        className="rounded-lg"
      />
    </div>
  );
}
```

## Converting TGS Files

### Using the Script

Use the provided script to convert TGS files to JSON:

```bash
./scripts/convert-tgs.sh input.tgs output.json
```

### Manual Conversion

```bash
# Decompress TGS file to JSON
gunzip -c animation.tgs > animation.json

# Move to public directory for web access
mv animation.json public/animations/
```

## File Structure

```
public/
  marketplace/
    Backyard.tgs      # Original TGS file
    Backyard.json     # Converted JSON file
src/
  components/
    TgsLottie.tsx     # Generic TGS component
    BackyardLottie.tsx # Specific Backyard component
scripts/
  convert-tgs.sh      # Conversion utility
```

## Props

### TgsLottie Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| src | string | required | Path to the JSON file |
| className | string | "" | CSS classes |
| width | number | 200 | Animation width in pixels |
| height | number | 200 | Animation height in pixels |
| loop | boolean | true | Whether to loop the animation |
| autoplay | boolean | true | Whether to start automatically |
| speed | number | 1 | Animation playback speed |

## Error Handling

The components include built-in error handling for:
- Failed network requests
- Invalid JSON data
- Missing animation files

Error states display user-friendly messages with appropriate styling.

## Performance Notes

- JSON files are fetched once and cached by the browser
- Large animations may impact performance on slower devices
- Consider using smaller dimensions for mobile devices
- The loading state shows a spinner while fetching data

## Example Usage in Marketplace

The Backyard animation is displayed on the marketplace page:

```tsx
// src/app/marketplace/page.tsx
<div className="mb-8 flex justify-center">
  <BackyardLottie 
    width={300} 
    height={300} 
    className="rounded-lg"
  />
</div>
```

This creates a centered, rounded animation that enhances the user experience when no gifts are found.
